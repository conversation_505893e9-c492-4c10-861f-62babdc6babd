<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced stock.move tree view for pending PO moves -->
    <record id="view_stock_move_tree_po_enhanced" model="ir.ui.view">
        <field name="name">stock.move.tree.po.enhanced</field>
        <field name="model">stock.move</field>
        <field name="arch" type="xml">
            <tree string="Pending PO Moves"
                  create="0"
                  edit="1"
                  default_order="date desc, id desc"
                  decoration-danger="state=='cancel'"
                  decoration-info="state=='assigned'"
                  decoration-muted="state=='draft'"
                  decoration-success="state=='done'"
                  decoration-warning="state in ('waiting','confirmed','partially_available')">

                <!-- Purchase Order Information -->
                <field name="reference" string="Reference"/>
                <field name="origin" string="Source Document"/>
                <field name="picking_id" string="Receipt"/>

                <!-- Product Information -->
                <field name="product_id" readonly="1"/>
                <field name="product_uom_qty" string="Demand"/>
                <field name="quantity_done" string="Done" optional="show"/>
                <field name="reserved_availability" string="Reserved" optional="show"/>
                <field name="product_uom" string="UoM"/>

                <!-- Status and Dates -->
                <field name="state"
                       widget="badge"
                       string="Status"
                       decoration-danger="state=='cancel'"
                       decoration-info="state=='assigned'"
                       decoration-muted="state=='draft'"
                       decoration-success="state=='done'"
                       decoration-warning="state in ('waiting','confirmed','partially_available')"/>
                <field name="date" string="Scheduled Date" optional="show"/>
                <field name="date_deadline" string="Deadline" optional="hide"/>

                <!-- Location Information -->
                <field name="location_id" string="From" optional="hide"/>
                <field name="location_dest_id" string="To" optional="hide"/>

                <!-- Additional Information -->
                <field name="company_id" optional="hide" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <!-- Enhanced action for pending PO moves -->
    <record id="action_stock_move_po_enhanced" model="ir.actions.act_window">
        <field name="name">Pending PO Moves</field>
        <field name="res_model">stock.move</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{
            'search_default_group_by_picking': 1,
            'search_default_group_by_state': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No pending purchase order moves found!
            </p>
            <p>
                This view shows stock moves from purchase orders with the following statuses:
                <ul>
                    <li><strong>Waiting:</strong> Waiting for availability</li>
                    <li><strong>Confirmed:</strong> Confirmed and ready</li>
                    <li><strong>Partially Available:</strong> Some quantity available</li>
                    <li><strong>Assigned:</strong> Fully available</li>
                </ul>
            </p>
        </field>
    </record>
</odoo>