from odoo import models, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Constants for purchase order statuses
    PO_PENDING_STATES = ['waiting', 'confirmed', 'partially_available', 'assigned']

    def action_view_pending_po_moves(self):
        """
        Display pending purchase order moves for the current product template.

        Shows stock moves with statuses: waiting, confirmed, partially_available, assigned
        This addresses the requirement to include 'Waiting Availability' status moves
        in the PO QTY button display.

        Returns:
            dict: Action configuration for opening the stock moves view
        """
        self.ensure_one()

        # Get supplier location with error handling
        try:
            supplier_location = self.env.ref("stock.stock_location_suppliers")
        except ValueError:
            raise UserError(_("Supplier location not found. Please check your stock configuration."))

        # Validate product has variants
        if not self.product_variant_ids:
            raise UserError(_("No product variants found for this template."))

        # Build domain for filtering stock moves
        domain = [
            ('picking_type_id.code', '=', 'incoming'),
            ('state', 'in', self.PO_PENDING_STATES),
            ('location_id', '=', supplier_location.id),
            ('product_id.product_tmpl_id', '=', self.id),
        ]

        # Prepare action configuration
        action = {
            'name': _('Pending PO Moves'),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.move',
            'view_mode': 'tree,form',
            'domain': domain,
            'context': {
                'create': False,
                'search_default_product_id': self.product_variant_ids[0].id,
                'default_product_tmpl_id': self.id,
            },
            'views': [
                (self.env.ref('product_po_status_enhanced_panfu.view_stock_move_tree_po_enhanced').id, 'tree'),
                (False, 'form')
            ],
            'target': 'current',
        }

        _logger.debug("Opening pending PO moves for product template %s", self.name)
        return action

    def get_moves(self, product_ids, extra_domain):
        """
        Override get_moves to include extended status states for PO Qty calculation.

        This method extends the original filtering to include 'waiting' status moves,
        addressing the business requirement to show 'Waiting Availability' status
        in PO quantity calculations.

        Args:
            product_ids (list): List of product template IDs
            extra_domain (list): Additional domain filters (currently ignored for consistency)

        Returns:
            list: Read group results with product quantities
        """
        if not product_ids:
            return []

        # Build optimized domain for purchase order moves
        domain = [
            ("picking_code", "=", "incoming"),
            ("location_id.usage", "=", "supplier"),
            ("product_id.product_tmpl_id", "in", product_ids),
            ("state", "in", self.PO_PENDING_STATES),
        ]

        # Note: extra_domain is intentionally ignored to maintain consistent
        # behavior with the enhanced status filtering requirements
        _logger.debug("Getting moves for products %s with enhanced status filtering", product_ids)

        result = self.env["stock.move"].read_group(
            domain,
            ["product_uom_qty", "reserved_availability"],
            ["product_id"]
        )

        return result